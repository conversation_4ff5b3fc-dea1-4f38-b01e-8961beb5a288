server:
  port: 7001
spring:
  application:
    name: goods-services #服务名称 必须要有
  main:
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.6.5:8848 #服务注册中心地址
        namespace: 5139d7fa-db43-42ae-9697-509bddd0cbd6 #命名空间
      config:
        server-addr: 192.168.6.5:8848 #配置中心地址
        file-extension: yaml #指定yaml格式的配置
        group: DEFAULT_GROUP
        namespace: 5139d7fa-db43-42ae-9697-509bddd0cbd6
